<?php
ob_start();

// Include session configuration before starting session
include("session_config.php");
session_start();

include("../admin/inc/config.php");
include("../admin/inc/functions.php");

// Initialize automatic cleanup system
include("auto_cleanup.php");

// Fetch FAQ data from database
$statement = $pdo->prepare("SELECT * FROM tbl_faq ORDER BY faq_id ASC");
$statement->execute();
$faqs = $statement->fetchAll(PDO::FETCH_ASSOC);

// Fetch settings
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = isset($settings['footer_copyright']) ? $settings['footer_copyright'] : "© 2025 SMART LIFE. All rights reserved.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frequently Asked Questions | SMART LIFE</title>
    <link rel="icon" type="image/png" href="../assets/uploads/logo.png">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#00c2ff',
                        'primary-dark': '#00a8e0',
                        'primary-light': '#e0f7ff',
                    },
                    fontFamily: {
                        sans: ['Sora', 'sans-serif'],
                    },
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'float': 'float 3s ease-in-out infinite',
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Sora:wght@300;400;500;600;700&display=swap');

        .bg-gradient-radial {
            background-image: radial-gradient(circle at center, rgba(0, 194, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
        }

        .faq-item {
            transition: all 0.3s ease;
        }

        .faq-item.active {
            border-color: #00c2ff;
            box-shadow: 0 4px 6px -1px rgba(0, 194, 255, 0.1), 0 2px 4px -1px rgba(0, 194, 255, 0.06);
        }

        @keyframes float {
            0% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
            100% {
                transform: translateY(0px);
            }
        }

        .search-highlight {
            background-color: #fef3c7;
            padding: 2px 4px;
            border-radius: 4px;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 font-sans">

    <!-- Header -->
    <header class="fixed inset-x-0 top-0 bg-white shadow z-50">
        <div class="container mx-auto px-4 flex items-center justify-between py-4">
            <a href="index.php" class="text-2xl font-bold text-gray-900">
                SMART LIFE<span class="text-blue-600">.</span>
            </a>
            <nav class="hidden md:flex items-center space-x-6">
                <a href="index.php#home" class="hover:text-blue-600 transition">Home</a>
                <a href="index.php#about" class="hover:text-blue-600 transition">About</a>
                <a href="all_products.php" class="hover:text-blue-600 transition">Products</a>
                <a href="index.php#gallery" class="hover:text-blue-600 transition">Best Deals</a>
                <a href="index.php#contact" class="hover:text-blue-600 transition">Contact</a>
                <a href="faq.php" class="text-primary font-medium">FAQs</a>

                <!-- Cart -->
                <a href="cart.php" class="relative text-xl hover:text-blue-600 transition">
                    🛒
                    <span class="absolute -top-1 -right-2 bg-blue-600 text-white text-xs rounded-full px-1 cart-count">0</span>
                </a>
            </nav>
            <!-- Mobile Menu Button -->
            <button id="mobileMenuButton" class="md:hidden flex items-center">
                <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M4 8h16M4 16h16"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- Mobile Menu -->
    <div id="mobileMenu" class="md:hidden fixed right-0 top-0 h-full w-1/2 bg-white z-40 transform translate-x-full transition-transform duration-300 ease-in-out shadow-lg">
        <div class="flex flex-col h-full">
            <div class="flex justify-between items-center p-4 border-b">
                <a href="index.php" class="text-xl font-bold text-gray-900">
                    SMART LIFE<span class="text-[#00c2ff]">.</span>
                </a>
                <button id="closeMobileMenu" class="text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <nav class="flex-1 p-4 space-y-4 overflow-y-auto">
                <a href="index.php#home" class="block text-gray-700 hover:text-[#00c2ff] transition">Home</a>
                <a href="index.php#about" class="block text-gray-700 hover:text-[#00c2ff] transition">About</a>
                <a href="all_products.php" class="block text-gray-700 hover:text-[#00c2ff] transition">Products</a>
                <a href="index.php#gallery" class="block text-gray-700 hover:text-[#00c2ff] transition">Best Deals</a>
                <a href="index.php#contact" class="block text-gray-700 hover:text-[#00c2ff] transition">Contact</a>
                <a href="faq.php" class="block text-[#00c2ff] font-medium">FAQs</a>

                <!-- Cart in Mobile Menu -->
                <a href="cart.php" class="flex items-center text-gray-700 hover:text-[#00c2ff] transition">
                    <span class="text-xl mr-2">🛒</span>
                    <span class="bg-[#00c2ff] text-white text-xs rounded-full px-2 py-1 cart-count">0</span>
                </a>
            </nav>
        </div>
    </div>

    <!-- Backdrop for mobile menu -->
    <div id="mobileMenuBackdrop" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30 hidden"></div>

    <!-- Hero Section -->
    <section class="pt-32 pb-16 relative overflow-hidden bg-gradient-to-b from-white to-blue-50">
        <div class="absolute inset-0 bg-gradient-radial pointer-events-none"></div>
        <div class="absolute right-0 top-0 w-64 h-64 bg-primary-light rounded-full filter blur-3xl opacity-20 -mr-32 -mt-32"></div>
        <div class="absolute left-0 bottom-0 w-64 h-64 bg-primary-light rounded-full filter blur-3xl opacity-20 -ml-32 -mb-32"></div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center max-w-3xl mx-auto">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Frequently Asked <span class="text-primary">Questions</span></h1>
                <p class="text-lg md:text-xl text-gray-600 mb-8">Find answers to common questions about our smart home products and services</p>

                <!-- Search Box -->
                <div class="max-w-md mx-auto mb-8">
                    <div class="relative">
                        <input type="text" id="faqSearch" placeholder="Search FAQs..." 
                               class="w-full px-4 py-3 pl-12 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <div class="flex flex-wrap justify-center gap-4 mt-8">
                    <a href="all_products.php" class="px-8 py-3 bg-primary hover:bg-primary-dark text-white font-medium rounded-full shadow-md hover:shadow-lg transition-all duration-300">
                        Shop Products <i class="fas fa-shopping-bag ml-2"></i>
                    </a>
                    <a href="index.php#contact" class="px-8 py-3 bg-white text-primary border border-primary rounded-full font-medium shadow-sm hover:bg-primary-light transition-all duration-300">
                        Contact Support <i class="fas fa-headset ml-2"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Decorative Elements -->
        <div class="absolute right-10 top-40 text-5xl text-primary opacity-10 animate-pulse-slow">
            <i class="fas fa-question-circle"></i>
        </div>
        <div class="absolute left-10 bottom-20 text-5xl text-primary opacity-10 animate-pulse-slow" style="animation-delay: 1s;">
            <i class="fas fa-lightbulb"></i>
        </div>
        <div class="absolute right-1/4 bottom-10 text-3xl text-primary opacity-10 animate-float">
            <i class="fas fa-home"></i>
        </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div id="faqContainer" class="space-y-6">
                    <?php if (!empty($faqs)): ?>
                        <?php foreach ($faqs as $index => $faq): ?>
                            <div class="faq-item bg-white rounded-xl p-6 shadow-sm border border-gray-200" data-faq-index="<?= $index ?>">
                                <button class="faq-question w-full flex justify-between items-center text-left focus:outline-none">
                                    <span class="text-lg font-semibold text-gray-900 pr-4"><?= htmlspecialchars($faq['faq_title']) ?></span>
                                    <i class="fas fa-chevron-down text-primary transition-transform duration-300 flex-shrink-0"></i>
                                </button>
                                <div class="faq-answer mt-4 text-gray-600 hidden">
                                    <p><?= nl2br(htmlspecialchars($faq['faq_content'])) ?></p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center py-12">
                            <i class="fas fa-question-circle text-6xl text-gray-300 mb-4"></i>
                            <h3 class="text-xl font-semibold text-gray-600 mb-2">No FAQs Available</h3>
                            <p class="text-gray-500">Check back later for frequently asked questions.</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- No Results Message -->
                <div id="noResults" class="text-center py-12 hidden">
                    <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">No Results Found</h3>
                    <p class="text-gray-500">Try searching with different keywords.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-primary-light relative overflow-hidden">
        <div class="absolute right-0 bottom-0 w-64 h-64 bg-primary rounded-full filter blur-3xl opacity-10"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center max-w-2xl mx-auto">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Still Have Questions?</h2>
                <p class="text-lg text-gray-600 mb-8">Our support team is here to help you with any questions about our smart home products.</p>
                <div class="flex flex-wrap justify-center gap-4">
                    <a href="index.php#contact" class="px-8 py-3 bg-primary hover:bg-primary-dark text-white font-medium rounded-full shadow-md hover:shadow-lg transition-all duration-300">
                        Contact Support <i class="fas fa-envelope ml-2"></i>
                    </a>
                    <a href="tel:+255123456789" class="px-8 py-3 bg-white text-primary border border-primary rounded-full font-medium shadow-sm hover:bg-gray-50 transition-all duration-300">
                        Call Us <i class="fas fa-phone ml-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <?php include 'includes/footer.php'; ?>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu functionality
            const mobileMenuButton = document.getElementById('mobileMenuButton');
            const mobileMenu = document.getElementById('mobileMenu');
            const closeMobileMenu = document.getElementById('closeMobileMenu');
            const mobileMenuBackdrop = document.getElementById('mobileMenuBackdrop');

            function openMobileMenu() {
                mobileMenu.classList.remove('translate-x-full');
                mobileMenuBackdrop.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }

            function closeMobileMenuFunc() {
                mobileMenu.classList.add('translate-x-full');
                mobileMenuBackdrop.classList.add('hidden');
                document.body.style.overflow = '';
            }

            if (mobileMenuButton) {
                mobileMenuButton.addEventListener('click', openMobileMenu);
            }

            if (closeMobileMenu) {
                closeMobileMenu.addEventListener('click', closeMobileMenuFunc);
            }

            if (mobileMenuBackdrop) {
                mobileMenuBackdrop.addEventListener('click', closeMobileMenuFunc);
            }

            // FAQ Accordion Functionality
            const faqQuestions = document.querySelectorAll('.faq-question');

            faqQuestions.forEach(question => {
                question.addEventListener('click', function() {
                    const faqItem = this.closest('.faq-item');
                    const answer = faqItem.querySelector('.faq-answer');
                    const icon = this.querySelector('i');

                    // Toggle active state
                    faqItem.classList.toggle('active');

                    // Toggle answer visibility
                    if (answer.classList.contains('hidden')) {
                        answer.classList.remove('hidden');
                        icon.style.transform = 'rotate(180deg)';
                    } else {
                        answer.classList.add('hidden');
                        icon.style.transform = 'rotate(0)';
                    }
                });
            });

            // FAQ Search Functionality
            const searchInput = document.getElementById('faqSearch');
            const faqContainer = document.getElementById('faqContainer');
            const noResults = document.getElementById('noResults');
            const allFaqItems = document.querySelectorAll('.faq-item');

            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase().trim();
                    let visibleCount = 0;

                    allFaqItems.forEach(item => {
                        const question = item.querySelector('.faq-question span').textContent.toLowerCase();
                        const answer = item.querySelector('.faq-answer p').textContent.toLowerCase();
                        
                        if (searchTerm === '' || question.includes(searchTerm) || answer.includes(searchTerm)) {
                            item.style.display = 'block';
                            visibleCount++;
                            
                            // Highlight search terms
                            if (searchTerm !== '') {
                                highlightSearchTerm(item, searchTerm);
                            } else {
                                removeHighlight(item);
                            }
                        } else {
                            item.style.display = 'none';
                        }
                    });

                    // Show/hide no results message
                    if (visibleCount === 0 && searchTerm !== '') {
                        noResults.classList.remove('hidden');
                        faqContainer.classList.add('hidden');
                    } else {
                        noResults.classList.add('hidden');
                        faqContainer.classList.remove('hidden');
                    }
                });
            }

            function highlightSearchTerm(item, term) {
                const question = item.querySelector('.faq-question span');
                const answer = item.querySelector('.faq-answer p');
                
                [question, answer].forEach(element => {
                    const text = element.textContent;
                    const regex = new RegExp(`(${term})`, 'gi');
                    const highlightedText = text.replace(regex, '<span class="search-highlight">$1</span>');
                    element.innerHTML = highlightedText;
                });
            }

            function removeHighlight(item) {
                const question = item.querySelector('.faq-question span');
                const answer = item.querySelector('.faq-answer p');
                
                [question, answer].forEach(element => {
                    element.innerHTML = element.textContent;
                });
            }
        });
    </script>

</body>
</html>
